import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  User, 
  LoginCredentials, 
  RegisterData, 
  AuthResponse, 
  UserUpdateData, 
  UserStats,
  STORAGE_KEYS 
} from '@/types/user';

// Simulated API delay
const API_DELAY = 1000;

// Mock user database (in a real app, this would be your backend API)
const mockUsers: User[] = [];

// Utility function to simulate API calls
const simulateApiCall = <T>(data: T, delay: number = API_DELAY): Promise<T> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), delay);
  });
};

// Generate unique ID
const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// Authentication Service
export class UserService {
  // Login user
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // Simulate API call
      await simulateApiCall(null, 800);

      // Find user by email (in real app, this would be handled by backend)
      const user = mockUsers.find(u => u.email.toLowerCase() === credentials.email.toLowerCase());
      
      if (!user) {
        return {
          success: false,
          error: 'User not found. Please check your email or sign up.',
        };
      }

      // In a real app, password would be verified on the backend
      // For demo purposes, we'll accept any password
      const token = `mock_token_${user.id}_${Date.now()}`;
      
      // Store authentication data
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
      await AsyncStorage.setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'true');

      return {
        success: true,
        user,
        token,
        message: 'Login successful',
      };
    } catch (error) {
      return {
        success: false,
        error: 'Login failed. Please try again.',
      };
    }
  }

  // Register new user
  static async register(userData: RegisterData): Promise<AuthResponse> {
    try {
      // Simulate API call
      await simulateApiCall(null, 1200);

      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email.toLowerCase() === userData.email.toLowerCase());
      if (existingUser) {
        return {
          success: false,
          error: 'An account with this email already exists.',
        };
      }

      // Validate password confirmation
      if (userData.password !== userData.confirmPassword) {
        return {
          success: false,
          error: 'Passwords do not match.',
        };
      }

      // Create new user
      const newUser: User = {
        id: generateId(),
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        university: userData.university || '',
        major: userData.major || '',
        year: userData.year || '',
        phone: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Add to mock database
      mockUsers.push(newUser);

      const token = `mock_token_${newUser.id}_${Date.now()}`;
      
      // Store authentication data
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(newUser));
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
      await AsyncStorage.setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'true');

      return {
        success: true,
        user: newUser,
        token,
        message: 'Account created successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: 'Registration failed. Please try again.',
      };
    }
  }

  // Get current user from storage
  static async getCurrentUser(): Promise<User | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Check if user is authenticated
  static async isAuthenticated(): Promise<boolean> {
    try {
      const isAuth = await AsyncStorage.getItem(STORAGE_KEYS.IS_AUTHENTICATED);
      const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      return isAuth === 'true' && !!token;
    } catch (error) {
      return false;
    }
  }

  // Update user profile
  static async updateProfile(userId: string, updateData: UserUpdateData): Promise<AuthResponse> {
    try {
      await simulateApiCall(null, 800);

      const currentUser = await this.getCurrentUser();
      if (!currentUser || currentUser.id !== userId) {
        return {
          success: false,
          error: 'User not found or unauthorized.',
        };
      }

      // Update user data
      const updatedUser: User = {
        ...currentUser,
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      // Update in mock database
      const userIndex = mockUsers.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        mockUsers[userIndex] = updatedUser;
      }

      // Update in storage
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(updatedUser));

      return {
        success: true,
        user: updatedUser,
        message: 'Profile updated successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update profile. Please try again.',
      };
    }
  }

  // Get user statistics (mock data)
  static async getUserStats(userId: string): Promise<UserStats> {
    await simulateApiCall(null, 500);
    
    // Mock statistics - in a real app, this would come from your backend
    return {
      totalReminders: Math.floor(Math.random() * 50) + 10,
      completedReminders: Math.floor(Math.random() * 30) + 5,
      pendingReminders: Math.floor(Math.random() * 15) + 2,
      weeklyReminders: Math.floor(Math.random() * 10) + 3,
    };
  }

  // Logout user
  static async logout(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.IS_AUTHENTICATED,
      ]);
    } catch (error) {
      console.error('Error during logout:', error);
    }
  }

  // Clear all user data (for testing/development)
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.clear();
      mockUsers.length = 0; // Clear mock database
    } catch (error) {
      console.error('Error clearing data:', error);
    }
  }
}
